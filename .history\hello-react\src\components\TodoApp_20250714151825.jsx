const products = [
  { id: 1, title: "Yumurta", quantity: 10, completed: true },
  { id: 2, title: "Zey<PERSON>", quantity: 1, completed: true },
  { id: 3, title: "Peynir", quantity: 2, completed: false },
  { id: 4, title: "Domates", quantity: 2, completed: true },
  { id: 5, title: "Salatalık", quantity: 3, completed: false },
];

export default function TodoApp() {
  return (
    <div className="container">
      <Header />
      <Form />
      <List />
      <Summary />
    </div>
  );
}

function Header() {
  return <h1>TodoApp</h1>;
}

function Form() {
  const [itemName, setItemName] = useState("aaa");

  function handleFormSubmit(event) {
    event.preventDefault();
    // console.log(event.target);
    console.log("Form submitted");
  }
  return (
    <form onSubmit={handleFormSubmit(event)}>
      <input type="text" placeholder="Eleman ekle" name="itemName" />
      <select>
        {Array.from({ length: 10 }, (v, i) => i).map((num) => (
          <option key={num} value={num}>
            {num}
          </option>
        ))}
      </select>
      <button type="button" onClick={handleFormSubmit}>
        Ekle
      </button>
    </form>
  );
}

function List() {
  return (
    <ul>
      {products.map((p, index) => (
        <ListItem item={p} key={p.id} />
      ))}
    </ul>
  );
}

function ListItem({ item }) {
  return (
    <li>
      <span style={item.completed ? { textDecoration: "line-through" } : {}}>
        {item.title} - {item.quantity}
      </span>
      <button>X</button>
    </li>
  );
}

function Summary() {
  return <div>Alışveriş sepetinizde 10 ürün bulunmaktadır.</div>;
}
