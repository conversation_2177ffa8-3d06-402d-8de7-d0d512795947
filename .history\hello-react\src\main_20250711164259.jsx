import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
//  import 'bootstrap/dist/css/bootstrap.min.css';

import App from './App.jsx';

function Navbar() {
  return (
    <header>
      <nav
        className="navbar navbar-expand bg-dark border-bottom border-boddy"
        data-bs-theme="dark"
      >
        <div className="container">
          <a href="#" className="navbar-brand">
            Pizza Store
          </a>
        </div>
      </nav>
    </header>
  );
}

function PizzaList() {
  const list=[];

  return (
    <div className="pizza-list">
      <h2>Pizza List</h2>
      <div className="row row-cols-2 row-cols-md-3 row-cols-xl-4 g-4">
        <Pizza />
        <Pizza />
        <Pizza />
        <Pizza />
        <Pizza />
        <Pizza />
        <Pizza />
        <Pizza />
      </div>
    </div>
  );
}

function Pizza() {
  return (
    <div className="col">
      <div className="card">
        <img
          src="/img/pizza1.jpg"
          alt=""
          className="card-img-top p-2 p-md-3 border-bottom"
        />
        <div className="card-body">
          <h2 className="card-title">Sucuklu Pizza</h2>
          <p className="card-text">Lorem ipsum dolar sit amet.</p>
          <span className="badge text-bg-primary">200 TL</span>
        </div>
      </div>
    </div>
  );
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App />
  </StrictMode>
);
