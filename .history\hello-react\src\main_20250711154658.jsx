import { StrictMode } from "react";
import { createRoot } from "react-dom/client";

function App() {
  return (
    <div>
      <Navbar />
      <PizzaList />
    </div>
  );
}
function Navbar() {
  return <h1>Navbar</h1>;
}

function PizzaList() {
  return (
    <div className="pizza-list">
      <h2>Pizza List</h2>
      <p>Pizza</p>
      <p>Pizza</p>
      <p>Pizza</p>
    </div>
  );
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App />
  </StrictMode>
);
