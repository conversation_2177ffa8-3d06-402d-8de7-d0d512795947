import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
//  import 'bootstrap/dist/css/bootstrap.min.css';

function App() {
  return (
    <>
      <Navbar />
      <div className="container">
        <PizzaList />
      </div>
    </>
  );
}
function Navbar() {
  return (
    <header>
      <nav
        className="navbar navbar-expand bg-dark border-bottom border-boddy"
        data-bs-theme="dark"
      >
        <div className="container">
          <a href="#" className="navbar-brand">
            Pizza Store
          </a>
        </div>
      </nav>
    </header>
  );
}

function PizzaList() {
  return (
    <div className="pizza-list">
      <h2>Pizza List</h2>
      <Pizza />
      <Pizza />
      <Pizza />
    </div>
  );
}

function Pizza() {
  return (
    <div className="card">
      <img
        src="/img/pizza1.jpg"
        alt=""
        className="card-img-top p-2 p-md-3 border-bottom"
      />
      <div className="card-boddy">
        <h2 className='card-title'>Sucuklu Pizza</h2>
        <p className='card-text'>Lorem ipsum dolar sit amet.</p>
        <span className='badge-test-bg-primary'>200 TL</span>
      </div>
      <p>Pizza</p>;
    </div>
  );
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App />
  </StrictMode>
);
