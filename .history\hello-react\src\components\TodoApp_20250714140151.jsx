export default function TodoApp() {
  return (
    <div className="container">
      <Header />
      <Form />
    </div>
  );
}

function Header() {
  return <h1>TodoApp</h1>;
}

function Form() {
  return (
    <form>
      <input type="text" placeholder="Eleman ekle" />
      <select>
        <option value="">Seçiniz</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
      </select>
      <button type="submit">Ekle</button>
    </form>
  );
}

function List() {
  return (
    <ul>
      <li>Eleman 1</li>
      <li>Eleman 2</li>
      <li>Eleman 3</li>
    </ul>
  );
}

function ListItem() {
  return (
    <li>
      <span>Yumurta</span>
      <button>X</button>
    </li>
  );
}
