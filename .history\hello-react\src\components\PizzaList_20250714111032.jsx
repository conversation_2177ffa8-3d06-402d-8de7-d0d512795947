import Pizza from "./Pizza";

export default function PizzaList() {
  const pizzas = [
    {
      title: "Karışık Pizza",
      description: "Karışık Pizza",
      image: "pizza2.jpg",
      price: 300,
    },
    {
      title: "Sucuklu Pizza",
      description: "Sucuklu Pizza",
      image: "pizza1.jpg",
      price: 400,
    },
    {
      title: "Acılı Pizza",
      description: "Acılı Pizza",
      image: "pizza3.jpg",
      price: 400,
    },
  ];

  return (
    <div className="pizza-list">
      <h2>Pizza List</h2>
      <div className="row row-cols-2 row-cols-md-3 row-cols-xl-4 g-4">
        {pizzas.length > 0 &&
          pizzas.map((p, index) => <Pizza pizzaObj={p} key={index} />)}
      </div>
    </div>
  );
}
