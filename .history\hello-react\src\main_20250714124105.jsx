import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import Events from "./components/Events.jsx";
import State from "./components/State.jsx";
//  import 'bootstrap/dist/css/bootstrap.min.css';
import App from "./components/App.jsx";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    {/* <App /> */}
    {/* <Events/> */}
    <State />
  </StrictMode>
);
