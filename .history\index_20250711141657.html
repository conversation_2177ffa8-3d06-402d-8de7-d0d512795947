<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script
      crossorigin
      src="https://unpkg.com/react@18/umd/react.development.js"
    ></script>
    <script
      crossorigin
      src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"
    ></script>

    <script
      crossorigin
      src="https://unpkg.com/react@18/umd/react.production.min.js"
    ></script>
    <script
      crossorigin
      src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"
    ></script>

    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <title>Document</title>
  </head>
  <body>

    <div id="root"></div>

    <script type="text/babel">
      const template=<h1<Hello React</h1>;
      const root=ReactDOM.createRoot(document.getElementById("root"));
      root.render(template);


    </script>
  </body>
</html>
