import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import 'bootstrap/dist/css/bootstrap.min.css';

function App() {
  return (
    <div>
      <Navbar />
      <PizzaList />
    </div>
  );
}
function Navbar() {
  return <h1>Navbar</h1>;
}

function PizzaList() {
  return (
    <div className="pizza-list">
      <h2>Pizza List</h2>
      <Pizza/>
      <Pizza/>
      <Pizza/>
    </div>
  );
}

function Pizza() {
  return (<p>Pizza</p>);
}

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App />
  </StrictMode>
);
